# 前端环境变量
# API 基础 URL
VITE_API_BASE_URL=http://localhost:3001/api
VITE_SERVER_URL=http://localhost:3001
VITE_ENV=development
VITE_BLOB_PUBLIC_URL=https://public-url-to-your-blob-storage.public.blob.vercel-storage.com

# 生产环境配置示例
# VITE_API_BASE_URL=/api
# VITE_SERVER_URL=
# VITE_ENV=production
# VITE_BLOB_PUBLIC_URL=https://your-production-blob-url.public.blob.vercel-storage.com

# 后端环境变量
PORT=3001                           # 后端服务器端口
NODE_ENV=development                # 环境标识（development/production）
BLOB_READ_WRITE_TOKEN=              # Vercel Blob服务的读写令牌

# 数据库环境变量
POSTGRES_PRISMA_URL=                # Prisma连接池URL，格式：postgres://user:password@host:port/database?pgbouncer=true&connection_limit=1
POSTGRES_URL_NON_POOLING=           # Prisma直接连接URL，格式：postgres://user:password@host:port/database