#!/usr/bin/env node

/**
 * 项目设置脚本
 * 自动化项目初始设置流程
 */

import { execSync } from 'child_process';
import { existsSync, copyFileSync, readFileSync, writeFileSync } from 'fs';
import { join } from 'path';

const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`\n${colors.bold}[步骤 ${step}]${colors.reset} ${colors.blue}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function runCommand(command, description) {
  try {
    log(`执行: ${command}`);
    execSync(command, { stdio: 'inherit' });
    logSuccess(`${description} 完成`);
    return true;
  } catch (error) {
    logError(`${description} 失败: ${error.message}`);
    return false;
  }
}

function checkNodeVersion() {
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 18) {
    logError(`Node.js 版本过低 (${nodeVersion})，需要 >= 18.0.0`);
    process.exit(1);
  }
  
  logSuccess(`Node.js 版本检查通过 (${nodeVersion})`);
}

function setupEnvironment() {
  const envExample = '.env.example';
  const envLocal = '.env.local';
  
  if (!existsSync(envExample)) {
    logError('找不到 .env.example 文件');
    return false;
  }
  
  if (existsSync(envLocal)) {
    logWarning('.env.local 已存在，跳过创建');
    return true;
  }
  
  try {
    copyFileSync(envExample, envLocal);
    logSuccess('已创建 .env.local 文件');
    
    log('\n请编辑 .env.local 文件，填入以下必需的环境变量：', 'yellow');
    log('- POSTGRES_PRISMA_URL (Neon 数据库连接 URL)', 'yellow');
    log('- POSTGRES_URL_NON_POOLING (Neon 数据库直连 URL)', 'yellow');
    log('- BLOB_READ_WRITE_TOKEN (Vercel Blob 存储令牌)', 'yellow');
    
    return true;
  } catch (error) {
    logError(`创建环境文件失败: ${error.message}`);
    return false;
  }
}

function checkEnvironmentVariables() {
  const envLocal = '.env.local';
  
  if (!existsSync(envLocal)) {
    logWarning('未找到 .env.local 文件，请先运行环境设置');
    return false;
  }
  
  const envContent = readFileSync(envLocal, 'utf8');
  const requiredVars = [
    'POSTGRES_PRISMA_URL',
    'POSTGRES_URL_NON_POOLING'
  ];
  
  const missingVars = requiredVars.filter(varName => {
    const regex = new RegExp(`^${varName}=.+`, 'm');
    return !regex.test(envContent);
  });
  
  if (missingVars.length > 0) {
    logWarning(`以下环境变量未配置: ${missingVars.join(', ')}`);
    return false;
  }
  
  logSuccess('环境变量检查通过');
  return true;
}

function setupDatabase() {
  log('\n正在设置数据库...');
  
  if (!runCommand('npx prisma generate', 'Prisma 客户端生成')) {
    return false;
  }
  
  if (!runCommand('npx prisma db push', '数据库模式同步')) {
    logWarning('数据库同步失败，请检查数据库连接配置');
    return false;
  }
  
  return true;
}

function createGitHooks() {
  if (existsSync('.git')) {
    runCommand('npx husky install', 'Git hooks 设置');
  } else {
    logWarning('未检测到 Git 仓库，跳过 Git hooks 设置');
  }
}

async function main() {
  log(`${colors.bold}${colors.blue}🚀 会通智能色彩云库 - 项目设置${colors.reset}\n`);
  
  // 步骤 1: 检查 Node.js 版本
  logStep(1, '检查 Node.js 版本');
  checkNodeVersion();
  
  // 步骤 2: 安装依赖
  logStep(2, '安装项目依赖');
  if (!runCommand('npm install', '依赖安装')) {
    process.exit(1);
  }
  
  // 步骤 3: 设置环境变量
  logStep(3, '设置环境变量');
  setupEnvironment();
  
  // 步骤 4: 检查环境变量
  logStep(4, '检查环境变量配置');
  const envConfigured = checkEnvironmentVariables();
  
  // 步骤 5: 设置数据库（仅在环境变量配置完成时）
  if (envConfigured) {
    logStep(5, '设置数据库');
    setupDatabase();
  } else {
    logWarning('跳过数据库设置，请先配置环境变量后手动运行: npx prisma db push');
  }
  
  // 步骤 6: 设置 Git hooks
  logStep(6, '设置 Git hooks');
  createGitHooks();
  
  // 完成
  log(`\n${colors.bold}${colors.green}🎉 项目设置完成！${colors.reset}\n`);
  
  if (envConfigured) {
    log('现在可以运行以下命令启动开发服务器：', 'green');
    log('npm run dev', 'bold');
  } else {
    log('请完成以下步骤：', 'yellow');
    log('1. 编辑 .env.local 文件，填入必需的环境变量', 'yellow');
    log('2. 运行: npx prisma db push', 'yellow');
    log('3. 运行: npm run dev', 'yellow');
  }
  
  log('\n访问地址：', 'blue');
  log('- 前端: http://localhost:5173', 'blue');
  log('- 后端 API: http://localhost:3001', 'blue');
  log('- 管理后台: http://localhost:5173/admin', 'blue');
}

main().catch(error => {
  logError(`设置过程中发生错误: ${error.message}`);
  process.exit(1);
});
