# 开发指南 (Development Guide)

本文档提供详细的开发环境设置和跨设备开发指南。

## 🔄 快速设置（推荐）

### 自动设置脚本

```bash
# 克隆项目
git clone https://github.com/Bavoch/huitong-material.git
cd huitong-material

# 运行自动设置脚本
npm run setup
```

自动设置脚本将：
- ✅ 检查 Node.js 版本
- ✅ 安装项目依赖
- ✅ 创建环境变量文件
- ✅ 设置数据库
- ✅ 配置 Git hooks

## 🖥️ 跨设备开发设置

### Windows 设置

```powershell
# 安装 Node.js (推荐使用 nvm-windows)
winget install OpenJS.NodeJS

# 克隆项目
git clone https://github.com/Bavoch/huitong-material.git
cd huitong-material

# 运行设置
npm run setup
```

### macOS 设置

```bash
# 安装 Node.js (推荐使用 nvm)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18

# 克隆项目
git clone https://github.com/Bavoch/huitong-material.git
cd huitong-material

# 运行设置
npm run setup
```

### Linux 设置

```bash
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 或使用 nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18

# 克隆项目
git clone https://github.com/Bavoch/huitong-material.git
cd huitong-material

# 运行设置
npm run setup
```

## 🔧 手动设置步骤

如果自动设置失败，可以按以下步骤手动设置：

### 1. 环境要求检查

```bash
# 检查 Node.js 版本 (需要 >= 18)
node --version

# 检查 npm 版本
npm --version

# 检查 Git 版本
git --version
```

### 2. 安装依赖

```bash
npm install
```

### 3. 环境变量配置

```bash
# 复制环境变量模板
cp .env.example .env.local

# 编辑环境变量文件
# Windows: notepad .env.local
# macOS/Linux: nano .env.local
```

必需的环境变量：

```env
# 开发环境
NODE_ENV=development
PORT=3001

# 数据库 (Neon PostgreSQL)
POSTGRES_PRISMA_URL=******************************************************************************
POSTGRES_URL_NON_POOLING=********************************************

# 文件存储 (Vercel Blob)
BLOB_READ_WRITE_TOKEN=your_blob_token_here
```

### 4. 数据库设置

```bash
# 生成 Prisma 客户端
npx prisma generate

# 同步数据库模式
npx prisma db push

# (可选) 打开数据库管理界面
npx prisma studio
```

### 5. 启动开发服务器

```bash
# 启动前端和后端
npm run dev

# 或分别启动
npm run dev:frontend  # 前端: http://localhost:5173
npm run dev:backend   # 后端: http://localhost:3001
```

## 🌐 网络配置

### 局域网访问设置

如需在局域网内其他设备访问开发服务器：

1. **修改 Vite 配置** (`vite.config.ts`):

```typescript
export default defineConfig({
  plugins: [react()],
  server: {
    host: '0.0.0.0', // 允许外部访问
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true
      }
    }
  },
})
```

2. **修改后端配置** (`backend/server.mjs`):

```javascript
const PORT = process.env.PORT || 3001;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server running on http://0.0.0.0:${PORT}`);
});
```

3. **获取本机 IP 地址**:

```bash
# Windows
ipconfig | findstr IPv4

# macOS/Linux
ifconfig | grep inet
```

4. **访问地址**:
   - 前端: `http://YOUR_IP:5173`
   - 后端: `http://YOUR_IP:3001`

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 端口占用

```bash
# 查找占用端口的进程
# Windows
netstat -ano | findstr :3001
taskkill /PID <PID> /F

# macOS/Linux
lsof -ti:3001
kill -9 <PID>
```

#### 2. 权限问题

```bash
# 清理 npm 缓存
npm cache clean --force

# 重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

#### 3. 数据库连接失败

- 检查 Neon 数据库 URL 格式
- 确认数据库服务状态
- 验证网络连接

#### 4. 文件上传失败

- 检查 Vercel Blob 配置
- 验证 BLOB_READ_WRITE_TOKEN
- 确认文件大小限制

### 环境重置

```bash
# 完全重置开发环境
rm -rf node_modules package-lock.json .env.local
npm install
npm run setup
```

## 📱 移动设备测试

### 移动端访问

1. 确保移动设备与开发机在同一网络
2. 使用开发机 IP 地址访问
3. 在移动浏览器中打开: `http://YOUR_IP:5173`

### 响应式测试

```bash
# 使用浏览器开发者工具
# Chrome: F12 -> 设备模拟器
# Firefox: F12 -> 响应式设计模式
```

## 🔄 代码同步

### Git 工作流

```bash
# 获取最新代码
git pull origin main

# 创建功能分支
git checkout -b feature/your-feature

# 提交更改
git add .
git commit -m "feat: your feature description"

# 推送分支
git push origin feature/your-feature
```

### 代码质量检查

```bash
# 运行 ESLint
npm run lint

# 检查未使用的导出
npm run lint:unused

# 自动修复代码格式
npm run lint -- --fix
```

## 📊 性能监控

### 开发工具

- **React DevTools**: 组件调试
- **Three.js Inspector**: 3D 场景调试
- **Network Tab**: API 请求监控
- **Performance Tab**: 性能分析

### 构建分析

```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## 🚀 部署准备

### 生产环境检查清单

- [ ] 环境变量配置完成
- [ ] 数据库连接正常
- [ ] 文件上传功能正常
- [ ] 3D 渲染性能良好
- [ ] 响应式设计测试通过
- [ ] 代码质量检查通过

### 部署到 Vercel

```bash
# 安装 Vercel CLI
npm i -g vercel

# 登录 Vercel
vercel login

# 部署项目
vercel --prod
```

## 📞 获取帮助

- **GitHub Issues**: 报告 Bug 或请求功能
- **开发文档**: 查看 README.md
- **API 文档**: 查看后端 API 注释
