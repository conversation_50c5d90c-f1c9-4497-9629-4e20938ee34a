# 会通材料展示系统

## 项目概述
会通材料展示系统是一个基于Web的3D材质渲染平台，允许用户上传3D模型并实时预览、编辑材质效果。系统支持预设材质库和自定义材质编辑，为用户提供直观的材质展示和调试体验。

## 主要功能

### 模型管理
- 支持GLB/GLTF格式的3D模型上传和预览
- 自动模型归一化处理（缩放、居中）
- 预设模型库选择

### 材质系统
- 预设材质库
  - 会通材料预设
  - 材质搜索功能
  - 材质缩略图预览
- 自定义材质编辑
  - 颜色调整
  - 金属度调整
  - 粗糙度调整
  - 透明度调整
  - 纹理贴图支持

### 场景控制
- 视角控制（旋转、缩放、平移）
- 默认视图重置
- 环境光照预设

### 渲染输出
- 渲染结果复制到剪贴板
- 渲染结果保存为图片

## 技术栈

### 前端
- React + TypeScript
- Three.js + React Three Fiber/Drei
- React Router
- 自定义UI组件库

### 后端
- Express.js
- Prisma ORM
- PostgreSQL
- Vercel Blob Storage (文件存储)

## 项目结构
```
src/
├── components/     # UI组件
├── pages/         # 页面组件
├── services/      # API服务
├── hooks/         # 自定义Hooks
├── assets/        # 静态资源
└── styles/        # 样式文件
```

## 核心功能实现
1. 模型处理：使用Three.js进行模型加载、归一化和渲染
2. 材质系统：基于MeshStandardMaterial实现材质属性调整
3. 状态管理：使用React Hooks管理组件状态和材质更新
4. 文件处理：支持模型文件上传和渲染结果导出

## 使用说明
1. 选择或上传3D模型
2. 点击可编辑材质区域选择要编辑的材质
3. 使用预设材质库或自定义编辑器调整材质属性
4. 通过鼠标操作调整视角（左键旋转、右键平移、滚轮缩放）
5. 使用工具栏保存或复制渲染结果